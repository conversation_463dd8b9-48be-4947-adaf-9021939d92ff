import sys
import os
# Add the repository root to Python path so 'app' module can be imported
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import boto3
import asyncio
import logging
import traceback
import io
import PyPDF2
from datetime import datetime
from typing import Dict, Any, <PERSON><PERSON>
from botocore.exceptions import ClientError
from pathlib import Path
from urllib.parse import urlparse
from app.core.configuration import settings


# Configure logging
logger = logging.getLogger(__name__)


def parse_s3_uri(s3_uri: str) -> Tuple[str, str]:
    """
    Parse S3 URI into bucket name and object key.

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key

    Returns:
        tuple: (bucket_name, object_key)

    Raises:
        ValueError: If URI format is invalid
    """
    try:
        parsed = urlparse(s3_uri)
        if parsed.scheme != 's3':
            raise ValueError(f"Invalid S3 URI scheme. Expected 's3', got '{parsed.scheme}'")

        bucket_name = parsed.netloc
        object_key = parsed.path.lstrip('/')

        if not bucket_name:
            raise ValueError("Bucket name is missing from S3 URI")
        if not object_key:
            raise ValueError("Object key is missing from S3 URI")

        return bucket_name, object_key

    except Exception as e:
        raise ValueError(f"Failed to parse S3 URI '{s3_uri}': {str(e)}")


class TextractProcessor:
    """
    AWS Textract processor for document analysis and text extraction.
    """

    def __init__(self, s3_client=None):
        """
        Initialize Textract processor with credentials from settings.
        """
        logger.info("🚀 Initializing TextractProcessor...")

        try:
            # Load AWS credentials from settings
            aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
            aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
            aws_region = settings.AWS_REGION

            self.textract_client = boto3.client(
                'textract',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            if s3_client:
                self.s3_client = s3_client
            else:
                self.s3_client = boto3.client(
                    's3',
                    region_name=aws_region,
                    aws_access_key_id=aws_access_key_id,
                    aws_secret_access_key=aws_secret_access_key
                )

            self.region = aws_region

            # Supported file formats for sync operations
            self.supported_formats = {'.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.tif'}
            self.max_file_size = 10 * 1024 * 1024  # 10 MB limit for sync operations

            # Validate AWS credentials are available
            if not aws_access_key_id and not aws_secret_access_key:
                logger.warning("⚠️  AWS credentials not provided - using default credential chain")

            logger.info("✅ TextractProcessor initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize TextractProcessor: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    def _get_file_metadata(self, s3_uri: str) -> Dict[str, Any]:
        """
        Get file metadata from S3.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: File metadata including size and content type
        """
        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)
            response = self.s3_client.head_object(Bucket=bucket_name, Key=object_key)
            return {
                'size': response['ContentLength'],
                'content_type': response.get('ContentType', ''),
                'last_modified': response['LastModified']
            }
        except ClientError as e:
            logger.error(f"❌ Failed to get file metadata: {str(e)}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def _get_file_name(self, s3_uri: str) -> str:
        """
        Extract file name from S3 URI.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            str: File name
        """
        try:
            _, object_key = parse_s3_uri(s3_uri)
            return Path(object_key).name
        except Exception as e:
            logger.error(f"❌ Failed to extract file name: {str(e)}")
            logger.error(f"   Traceback: {traceback.format_exc()}")
            raise

    def _count_pdf_pages(self, s3_uri: str) -> int:
        """
        Count pages in a PDF file from S3.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            int: Number of pages in PDF, or 1 if unable to determine
        """
        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)
            # Download PDF content
            response = self.s3_client.get_object(Bucket=bucket_name, Key=object_key)
            pdf_content = response['Body'].read()

            # Create PDF reader
            pdf_stream = io.BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_stream)

            page_count = len(pdf_reader.pages)
            return page_count

        except Exception as e:
            logger.warning(f"⚠️  Failed to count PDF pages: {str(e)}")
            logger.warning("   Assuming 1 page for sync processing")
            return 1

    def _should_use_sync_method(self, s3_uri: str) -> Tuple[bool, str]:
        """
        Determine if sync method should be used based on file criteria.

        Criteria for sync method:
        - File extension is supported (.pdf, .jpg, .jpeg, .png, .tiff, .tif)
        - File size is under 10 MB limit
        - For PDF: single page only (calculated, not assumed)
        - For TIFF: single page assumed

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            tuple: (can_use_sync, reason)
        """
        try:
            _, object_key = parse_s3_uri(s3_uri)

            # Get file metadata
            metadata = self._get_file_metadata(s3_uri)
            file_size = metadata['size']
            file_size_mb = file_size / (1024 * 1024)

            # Check file extension
            file_ext = Path(object_key).suffix.lower()
            if file_ext not in self.supported_formats:
                reason = f"Unsupported format: {file_ext}. Supported: {self.supported_formats}"
                logger.info(f"    Decision: ASYNC - {reason}")
                return False, reason

            # Check file size
            if file_size > self.max_file_size:
                max_size_mb = self.max_file_size / (1024 * 1024)
                reason = f"File size ({file_size_mb:.2f} MB) exceeds {max_size_mb:.0f} MB limit"
                logger.info(f"    Size: {file_size_mb:.2f} MB")
                logger.info(f"    Decision: ASYNC - {reason}")
                return False, reason

            # For PDF, count actual pages
            if file_ext == '.pdf':
                page_count = self._count_pdf_pages(s3_uri)
                if page_count > 1:
                    reason = f"PDF has {page_count} pages (sync supports single page only)"
                    logger.info(f"    Decision: ASYNC - {reason}")
                    return False, reason

            # For TIFF, assume single page (could be enhanced later)
            elif file_ext in ['.tiff', '.tif']:
                logger.info(f"    TIFF detected. Assuming single page for sync processing.")

            reason = f"File suitable for synchronous processing"
            logger.info(f"    Size: {file_size_mb:.2f} MB")
            logger.info(f"    Format: {file_ext} (supported)")
            logger.info(f"    Decision: SYNC - {reason}")

            return True, reason

        except Exception as e:
            reason = f"Failed to analyze file for sync/async decision: {str(e)}"
            logger.warning(f"⚠️  {reason}")
            logger.warning(f"    Traceback: {traceback.format_exc()}")
            logger.warning("    Defaulting to ASYNC method")
            return False, reason

    def analyze_document_sync(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using synchronous Textract detect_document_text.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results
        """

        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)

            # Use detect_document_text for sync processing
            response = self.textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )

            return response

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ SYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during SYNC Textract analysis: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    async def analyze_document_async(self, s3_uri: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for text extraction only (no tables/forms).

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting ASYNC Textract text detection...")

        try:
            bucket_name, object_key = parse_s3_uri(s3_uri)

            # Start document text detection (text only, no tables/forms)
            response = self.textract_client.start_document_text_detection(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )

            job_id = response['JobId']
            logger.info(f"✅ Textract text detection started successfully")
            logger.info(f"    Job ID: {job_id}")

            # Poll for completion
            logger.info("⏳ Waiting for Textract text detection to complete...")
            poll_count = 0
            start_time = datetime.now()

            while True:
                poll_count += 1
                elapsed_time = (datetime.now() - start_time).total_seconds()

                result = self.textract_client.get_document_text_detection(JobId=job_id)
                status = result['JobStatus']

                logger.info(f"    Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time:.1f}s)")

                if status == 'SUCCEEDED':
                    logger.info(f"✅ ASYNC Textract text detection completed successfully!")
                    logger.info(f"    Total processing time: {elapsed_time:.1f} seconds")
                    logger.info(f"    Total polls: {poll_count}")
                    return result

                elif status == 'FAILED':
                    error_msg = f"Textract text detection failed after {elapsed_time:.1f} seconds"
                    logger.error(f"❌ {error_msg}")
                    logger.error(f"    Job ID: {job_id}")
                    raise Exception(error_msg)

                elif status in ['IN_PROGRESS']:
                    logger.debug(f"    ⏳ Text detection still in progress... waiting 5 seconds")
                    await asyncio.sleep(3)
                else:
                    logger.warning(f"    ⚠️  Unknown status: {status}")
                    await asyncio.sleep(3)

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ ASYNC Textract ClientError: {error_code} - {error_message}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during ASYNC Textract text detection: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    def _convert_textract_to_structured_format(self, textract_response: Dict[str, Any]) -> str:
        """
        Convert Textract response to structured text format with coordinates.

        Args:
            textract_response: Textract API response

        Returns:
            str: Structured text with coordinates in CSV format
        """
        try:
            blocks = textract_response.get('Blocks', [])

            # Extract text blocks with coordinates in the required format
            text_lines = []

            # Process LINE blocks for text with coordinates (most efficient)
            for block in blocks:
                if block['BlockType'] == 'LINE':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)

                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # If no LINE blocks found (sync response), use WORD blocks
            if not text_lines:
                logger.info("    No LINE blocks found, using WORD blocks for text extraction")
                for block in blocks:
                    if block['BlockType'] == 'WORD':
                        bbox = block.get('Geometry', {}).get('BoundingBox', {})
                        text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues

                        # Convert to x1, y1, x2, y2 format
                        x1 = bbox.get('Left', 0)
                        y1 = bbox.get('Top', 0)
                        x2 = x1 + bbox.get('Width', 0)
                        y2 = y1 + bbox.get('Height', 0)

                        text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")

            # Build the structured text format (text only, no tables for speed)
            structured_text_parts = []

            # Add header
            structured_text_parts.append("=== TEXT WITH COORDINATES ===")
            structured_text_parts.append("text, x1, y1, x2, y2")

            # Add text lines
            for line in text_lines:
                structured_text_parts.append(line)

            structured_text = "\n".join(structured_text_parts)

            logger.info(f"    Extracted {len(text_lines)} text lines with coordinates")
            return structured_text

        except Exception as e:
            logger.error(f"❌ Error converting Textract response to structured format: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    
    async def process_document(self, s3_uri: str) -> Dict[str, Any]:
        """
        Complete Textract processing: tries sync first, then async if needed.

        Args:
            s3_uri: S3 URI in format s3://bucket-name/object-key

        Returns:
            dict: Complete Textract processing results
        """
        # Input validation
        if not s3_uri or not isinstance(s3_uri, str):
            raise ValueError("s3_uri must be a non-empty string")

        if not s3_uri.startswith('s3://'):
            raise ValueError("s3_uri must start with 's3://'")

        logger.info(f"🎯 Starting Textract processing: {s3_uri}")

        start_time = datetime.now()
        processing_method = "UNKNOWN"

        try:
            # Determine processing method based on file criteria
            should_use_sync, reason = self._should_use_sync_method(s3_uri)

            file_name = self._get_file_name(s3_uri)

            if should_use_sync:
                processing_method = "SYNC"
                try:
                    # Try sync method first
                    textract_result = self.analyze_document_sync(s3_uri)

                except Exception as sync_error:
                    logger.warning(f"⚠️  SYNC processing failed: {str(sync_error)}")
                    logger.warning(f"    Sync failure reason: {reason}")
                    logger.warning(f"    Traceback: {traceback.format_exc()}")
                    logger.info("🔄 Falling back to ASYNC processing...")
                    processing_method = "ASYNC_FALLBACK"
                    textract_result = await self.analyze_document_async(s3_uri)
                    logger.info("✅ ASYNC fallback processing successful!")
            else:
                logger.info(f"🚀 Using ASYNC processing directly... Reason: {reason}")
                processing_method = "ASYNC"
                textract_result = await self.analyze_document_async(s3_uri)
                logger.info("✅ ASYNC processing successful!")

            # Extract structured text with coordinates
            structured_text = self._convert_textract_to_structured_format(textract_result)

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "textract_metadata": {
                    "s3_uri": s3_uri,
                    "file_name": file_name,
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "processing_method": processing_method,
                    "aws_region": self.region
                },
                "textract_response_object": textract_result,
                "structured_text": structured_text
            }

            logger.info("✅ Textract processing completed successfully!")
            logger.info(f"    Total processing time for Textract: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Textract processing failed!")
            logger.error(f"    Processing method attempted: {processing_method}")
            logger.error(f"    Error: {str(e)}")
            logger.error(f"    Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise



async def process_document_with_textract(s3_uri: str, s3_client=None) -> Dict[str, Any]:
    """
    Process document using intelligent Textract (sync first, then async fallback).

    This function automatically determines the best processing method:
    - For supported formats (PDF, JPG, PNG, TIFF) under 10MB: tries sync first, falls back to async
    - For other files or larger files: uses async directly

    Args:
        s3_uri: S3 URI in format s3://bucket-name/object-key

    Returns:
        dict: Textract processing results with processing method metadata
    """
    processor = TextractProcessor(s3_client)
    return await processor.process_document(s3_uri)



if __name__ == "__main__":
    # Configure logger to print to console
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[logging.StreamHandler()]
    )
    # Example usage
    s3_uri = 's3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'
    asyncio.run(process_document_with_textract(s3_uri))

