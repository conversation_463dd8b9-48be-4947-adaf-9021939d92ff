"""
Worker to process S3 events from an SQS queue.
"""
import sys
import json
import time
from io import BytesIO
import boto3
from botocore.exceptions import ClientError
from loguru import logger
import os
from pathlib import Path
import hashlib

# Add project root to the Python path to allow for absolute imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.bda import main as llm_main
# NOTE: You would import your ingestion service here once it's created.
# from app.services.ingestion_service import process_document


class S3EventProcessor:
    """
    A worker that processes S3 upload events from an SQS queue.
    """
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL must be configured in your .env file to run the worker.")
        
        # Pass None instead of empty strings to allow boto3 to use its credential provider chain
        aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None

        self.output_folder = settings.EXTRACTION_FOLDER_OUTPUT_PREFIX
        self.sqs_client = boto3.client(
            "sqs",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )
        self.queue_url = queue_url
        logger.info(f"Worker initialized for SQS queue: {queue_url}")

    def process_messages(self):
        """
        Continuously poll the SQS queue for messages and process them.
        """
        logger.info("Starting S3 event processor worker...")
        while True:
            try:
                # Use long-polling to efficiently wait for messages
                response = self.sqs_client.receive_message(
                    QueueUrl=self.queue_url,
                    MaxNumberOfMessages=10,  # Process up to 10 messages at a time
                    WaitTimeSeconds=20,      # Enable long polling
                    MessageAttributeNames=['All']
                )

                messages = response.get('Messages', [])
                if not messages:
                    logger.debug("No new messages. Waiting...")
                    continue

                for message in messages:
                    receipt_handle = message['ReceiptHandle']
                    try:
                        logger.info(f"Received message: {message['MessageId']}")
                        body = json.loads(message['Body'])
                        
                        # S3 event messages might be wrapped in an SNS format
                        s3_event = json.loads(body['Message']) if 'Message' in body else body

                        if 'Records' not in s3_event:
                            logger.warning("Message is not a valid S3 event. Skipping.")
                            self._delete_message(receipt_handle)
                            continue

                        for record in s3_event['Records']:
                            bucket_name = record['s3']['bucket']['name']
                            object_key = record['s3']['object']['key']
                            
                            logger.info(f"Processing file: s3://{bucket_name}/{object_key}")
                            
                            # This is where you would call your ingestion logic
                            self._handle_ingestion(bucket_name, object_key)

                        # Delete the message from the queue after successful processing
                        self._delete_message(receipt_handle)

                    except Exception as e:
                        logger.error(f"Error processing message {message['MessageId']}: {e}")
                        # Do not delete the message, let it be re-processed after visibility timeout

            except ClientError as e:
                logger.error(f"SQS client error: {e}")
                time.sleep(10) # Wait before retrying

    def _handle_ingestion(self, bucket_name: str, object_key: str):
        """
        Triggers ingestion for a file, adds metadata, and uploads the resulting JSON.

        This function calls the main LLM processing logic with the S3 URI.
        The JSON response is then augmented with the original S3 object key
        and uploaded back to a designated folder in S3.
        """
        try:
            s3_uri = f"s3://{bucket_name}/{object_key}"
            output_s3_uri = f"s3://{bucket_name}/output/"

            logger.info(f"Triggering ingestion for {s3_uri} to {output_s3_uri}")

            # Call the LLM processing logic
            json_response = llm_main(input_s3_uri=s3_uri, output_s3_uri=output_s3_uri)

            logger.info(f"LLM processing complete for {object_key}. Response: {json_response}")

            if not json_response:
                logger.warning(f"LLM process returned no data for {object_key}. Skipping JSON upload.")
                return

            # Fetch original filename from S3 metadata
            original_filename = None
            try:
                head_object = s3_service.s3_client.head_object(Bucket=bucket_name, Key=object_key)
                original_filename = head_object.get("Metadata", {}).get("original-filename")
            except Exception as meta_error:
                logger.warning(f"Could not retrieve metadata for {object_key}: {meta_error}")

            # Add keys to response
            json_response["s3_object_key"] = object_key
            if original_filename:
                json_response["original_filename"] = original_filename
            else:
                # Fallback to basename if metadata is missing
                json_response["original_filename"] = os.path.basename(object_key)

            # Generate unique JSON output filename using hash
            base_filename = os.path.splitext(os.path.basename(object_key))[0]
            hash_suffix = hashlib.md5(object_key.encode('utf-8')).hexdigest()[:8]
            json_filename = f"{base_filename}_{hash_suffix}.json"
            output_json_key = f"{self.output_folder}/{json_filename}"

            logger.info(f"Uploading processed JSON to s3://{bucket_name}/{output_json_key}")

            # Convert dict to JSON bytes and upload
            json_bytes = json.dumps(json_response, indent=4).encode('utf-8')
            s3_service.s3_client.upload_fileobj(
                Fileobj=BytesIO(json_bytes),
                Bucket=bucket_name,
                Key=output_json_key,
                ExtraArgs={'ContentType': 'application/json'}
            )

            logger.success(f"Successfully processed and uploaded result for {s3_uri}")

        except Exception as e:
            logger.error(f"Failed to ingest {s3_uri}: {e}")
            raise

    def _delete_message(self, receipt_handle: str):
        """Delete a message from the SQS queue."""
        try:
            self.sqs_client.delete_message(QueueUrl=self.queue_url, ReceiptHandle=receipt_handle)
            logger.info(f"Message with handle {receipt_handle[:10]}... deleted.")
        except ClientError as e:
            logger.error(f"Failed to delete message: {e}")


if __name__ == "__main__":
    # This allows running the worker as a standalone script.
    # From the project root, run: python -m app.services.s3_event_processor
    worker = S3EventProcessor(queue_url=settings.SQS_EXTRACTION_QUEUE_URL, region_name=settings.AWS_REGION)
    worker.process_messages()